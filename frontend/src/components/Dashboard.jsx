import React, { useState, useCallback } from 'react'
import { Responsive, WidthProvider } from 'react-grid-layout'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { motion, AnimatePresence } from 'framer-motion'
import { RefreshCw, Layout, Save } from 'lucide-react'
import toast from 'react-hot-toast'

// Import widgets
import WeatherWidget from './widgets/WeatherWidget'
import BillsWidget from './widgets/BillsWidget'
import EventsWidget from './widgets/EventsWidget'
import ContactsWidget from './widgets/ContactsWidget'
import TodosWidget from './widgets/TodosWidget'
import ChartWidget from './widgets/ChartWidget'

// Import API functions
import { apiHelpers, queryKeys } from '../api/client'
import { useAuthStore } from '../utils/useAuth'

// Import CSS for react-grid-layout
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'

// Create responsive grid layout component
const ResponsiveGridLayout = WidthProvider(Responsive)

// Widget components mapping
const WIDGET_COMPONENTS = {
  'weather-current': WeatherWidget,
  'weather-forecast': WeatherWidget,
  'bills-upcoming': BillsWidget,
  'events-upcoming': EventsWidget,
  'bills-chart': ChartWidget,
  'todos': TodosWidget,
  'contacts': ContactsWidget,
}

// Widget titles
const WIDGET_TITLES = {
  'weather-current': 'Current Weather',
  'weather-forecast': '3-Day Forecast',
  'bills-upcoming': 'Upcoming Bills & Vacations',
  'events-upcoming': 'Upcoming Events',
  'bills-chart': 'Bills by Category',
  'todos': 'To-Do List',
  'contacts': 'Contact Status',
}

const Dashboard = () => {
  const queryClient = useQueryClient()
  const user = useAuthStore((state) => state.user)
  const [isEditMode, setIsEditMode] = useState(false)
  const [layouts, setLayouts] = useState(null)
  const [hasLayoutChanges, setHasLayoutChanges] = useState(false)

  // Fetch dashboard data
  const { data: dashboardData, isLoading, isError, refetch } = useQuery(
    queryKeys.dashboard.all,
    () => apiHelpers.get('/dashboard'),
    {
      staleTime: 1000 * 60 * 2, // 2 minutes
      cacheTime: 1000 * 60 * 5, // 5 minutes
      onSuccess: (data) => {
        // Set initial layouts from user preferences
        if (data.layout?.layouts && !layouts) {
          setLayouts(data.layout.layouts)
        }
      },
    }
  )

  // Save layout mutation
  const saveLayoutMutation = useMutation(
    (layouts) => apiHelpers.post('/dashboard/layout', { layouts }),
    {
      onSuccess: () => {
        toast.success('Layout saved successfully')
        setHasLayoutChanges(false)
        setIsEditMode(false)
        // Update user's layout in store
        useAuthStore.getState().updateUser({ dashboard_layout: { layouts } })
      },
      onError: () => {
        toast.error('Failed to save layout')
      },
    }
  )

  // Handle layout change
  const handleLayoutChange = useCallback((layout, layouts) => {
    setLayouts(layouts)
    setHasLayoutChanges(true)
  }, [])

  // Save layout
  const saveLayout = () => {
    if (layouts) {
      saveLayoutMutation.mutate(layouts)
    }
  }

  // Toggle edit mode
  const toggleEditMode = () => {
    if (isEditMode && hasLayoutChanges) {
      // Confirm before discarding changes
      const confirmDiscard = window.confirm('Discard unsaved layout changes?')
      if (!confirmDiscard) return
    }
    setIsEditMode(!isEditMode)
    if (!isEditMode) {
      setHasLayoutChanges(false)
    }
  }

  // Refresh dashboard data
  const refreshDashboard = () => {
    refetch()
    toast.success('Dashboard refreshed')
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary-200 dark:border-primary-800 border-t-primary-600 dark:border-t-primary-400 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">Failed to load dashboard</p>
          <button onClick={refreshDashboard} className="btn-primary">
            Try Again
          </button>
        </div>
      </div>
    )
  }

  // Default layouts if not set
  const currentLayouts = layouts || dashboardData?.layout?.layouts || user?.dashboard_layout?.layouts

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Dashboard Header */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Welcome back, {user?.username}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Edit Layout Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleEditMode}
              className={`btn ${isEditMode ? 'btn-primary' : 'btn-outline'} flex items-center gap-2`}
            >
              <Layout className="w-4 h-4" />
              <span className="hidden sm:inline">
                {isEditMode ? 'Done Editing' : 'Edit Layout'}
              </span>
            </motion.button>

            {/* Save Layout Button */}
            <AnimatePresence>
              {isEditMode && hasLayoutChanges && (
                <motion.button
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={saveLayout}
                  disabled={saveLayoutMutation.isLoading}
                  className="btn-primary flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  <span className="hidden sm:inline">Save Layout</span>
                </motion.button>
              )}
            </AnimatePresence>

            {/* Refresh Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95, rotate: 180 }}
              onClick={refreshDashboard}
              className="btn-ghost p-2"
              aria-label="Refresh dashboard"
            >
              <RefreshCw className="w-5 h-5" />
            </motion.button>
          </div>
        </div>

        {/* Edit Mode Notice */}
        <AnimatePresence>
          {isEditMode && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mb-4 p-4 bg-primary-100 dark:bg-primary-900/30 rounded-lg border border-primary-200 dark:border-primary-800"
            >
              <p className="text-sm text-primary-700 dark:text-primary-300">
                <strong>Edit Mode:</strong> Drag widgets to rearrange, resize from corners. 
                Click "Save Layout" when done.
              </p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Grid Layout */}
        <ResponsiveGridLayout
          className="layout"
          layouts={currentLayouts}
          onLayoutChange={handleLayoutChange}
          isDraggable={isEditMode}
          isResizable={isEditMode}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
          cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
          rowHeight={60}
          margin={[16, 16]}
          containerPadding={[0, 0]}
          autoSize={true}
        >
          {Object.keys(WIDGET_COMPONENTS).map((widgetKey) => {
            const WidgetComponent = WIDGET_COMPONENTS[widgetKey]
            if (!WidgetComponent) return null

            return (
              <motion.div
                key={widgetKey}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className={`glass-card p-4 ${isEditMode ? 'cursor-move' : ''}`}
              >
                {/* Widget Header */}
                <div className="mb-3 pb-3 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {WIDGET_TITLES[widgetKey]}
                  </h2>
                </div>

                {/* Widget Content */}
                <div className="widget-content">
                  <WidgetComponent
                    widgetId={widgetKey}
                    data={dashboardData}
                    isEditMode={isEditMode}
                  />
                </div>
              </motion.div>
            )
          })}
        </ResponsiveGridLayout>
      </div>

      {/* Custom styles for grid */}
      <style jsx>{`
        .react-grid-item.react-draggable-dragging {
          z-index: 100;
          opacity: 0.9;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .react-grid-item.resizing {
          opacity: 0.9;
        }

        .widget-content {
          height: calc(100% - 60px);
          overflow: auto;
        }

        .widget-content::-webkit-scrollbar {
          width: 6px;
        }

        .widget-content::-webkit-scrollbar-track {
          background: transparent;
        }

        .widget-content::-webkit-scrollbar-thumb {
          background: #d1d5db;
          border-radius: 3px;
        }

        .dark .widget-content::-webkit-scrollbar-thumb {
          background: #4b5563;
        }
      `}</style>
    </div>
  )
}

export default Dashboard