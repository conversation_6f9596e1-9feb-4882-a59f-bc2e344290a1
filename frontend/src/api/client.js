import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth and logging
apiClient.interceptors.request.use(
  (config) => {
    // Add timestamp to requests for debugging
    config.metadata = { startTime: new Date() }
    
    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, config.data)
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = new Date() - response.config.metadata.startTime
    
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(
        `✅ ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`,
        response.data
      )
    }
    
    // Extract data from response
    return response.data
  },
  (error) => {
    // Calculate request duration even for errors
    const duration = error.config?.metadata?.startTime
      ? new Date() - error.config.metadata.startTime
      : 0
    
    // Log error in development
    if (import.meta.env.DEV) {
      console.error(
        `❌ ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`,
        error.response?.data || error.message
      )
    }
    
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
            toast.error('Session expired. Please login again.')
          }
          break
          
        case 403:
          toast.error('You do not have permission to perform this action.')
          break
          
        case 404:
          toast.error('The requested resource was not found.')
          break
          
        case 429:
          toast.error('Too many requests. Please try again later.')
          break
          
        case 500:
          toast.error('Server error. Please try again later.')
          break
          
        default:
          // Use server's error message if available
          if (data?.message) {
            toast.error(data.message)
          } else {
            toast.error(`Request failed with status ${status}`)
          }
      }
    } else if (error.request) {
      // Request made but no response received
      toast.error('Network error. Please check your connection.')
    } else {
      // Error in request configuration
      toast.error('An unexpected error occurred.')
    }
    
    return Promise.reject(error)
  }
)

// Helper functions for common request patterns
export const apiHelpers = {
  // GET request
  get: (url, config = {}) => apiClient.get(url, config),
  
  // POST request
  post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
  
  // PATCH request
  patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
  
  // DELETE request
  delete: (url, config = {}) => apiClient.delete(url, config),
  
  // Upload file
  upload: (url, formData, onProgress) => {
    return apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      },
    })
  },
}

// Export the configured client
export default apiClient

// Utility function to handle API errors in components
export const handleApiError = (error, customMessage) => {
  if (import.meta.env.DEV) {
    console.error('API Error:', error)
  }
  
  // Don't show toast if interceptor already handled it
  if (!error.response || error.response.status === 401) {
    return
  }
  
  if (customMessage) {
    toast.error(customMessage)
  }
}

// Utility function to create query keys for React Query
export const queryKeys = {
  auth: {
    check: ['auth-check'],
    user: ['auth-user'],
  },
  dashboard: {
    all: ['dashboard'],
    layout: ['dashboard-layout'],
  },
  bills: {
    all: ['bills'],
    detail: (id) => ['bills', id],
    upcoming: ['bills-upcoming'],
    byCategory: ['bills-by-category'],
  },
  events: {
    all: ['events'],
    detail: (id) => ['events', id],
    upcoming: ['events-upcoming'],
  },
  contacts: {
    all: ['contacts'],
    detail: (id) => ['contacts', id],
    types: ['contact-types'],
  },
  todos: {
    all: ['todos'],
    detail: (id) => ['todos', id],
  },
  weather: {
    all: ['weather'],
    city: (city) => ['weather', city],
  },
  user: {
    profile: ['user-profile'],
    preferences: ['user-preferences'],
  },
}

// Utility function for optimistic updates
export const optimisticUpdate = (queryClient, queryKey, updater) => {
  queryClient.setQueryData(queryKey, updater)
  
  return {
    onError: (err, variables, context) => {
      // Revert on error
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData)
      }
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries(queryKey)
    },
  }
}